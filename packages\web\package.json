{"name": "apiflow", "description": "Apiflow is a visual API design tool that allows users to create, manage, and visualize APIs in a graphical way.", "author": "TrueLeaf Team", "license": "MIT", "private": true, "version": "0.9.0", "type": "module", "scripts": {"dev": "vite --host --mode standalone", "dev:standalone": "vite --host --mode standalone", "web": "vite --host -- -- web", "share": "vite --host --config build/share/vite.config.ts", "build:share": "vite build --config build/share/vite.config.ts", "build:web": "vite build --mode standalone", "build:standalone:pack": "vite build --mode standalone && electron-builder --dir", "build:standalone": "vite build --mode standalone && electron-builder", "build:standalone:win": "vite build --mode standalone && electron-builder --win", "build:standalone:mac": "vite build --mode standalone && electron-builder --mac", "build:standalone:linux": "vite build --mode standalone && electron-builder --linux", "preview": "vite preview", "build:font": "node build/share/script/build-font.mjs", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@tiptap/core": "^3.3.0", "@tiptap/extension-history": "^3.3.0", "@tiptap/starter-kit": "^3.3.0", "@tiptap/vue-3": "^3.3.0", "@types/ws": "^8.18.1", "axios": "^1.7.3", "brace": "^0.11.1", "dayjs": "^1.11.12", "dexie": "^4.0.8", "docx": "^9.5.1", "element-plus": "^2.11.1", "file-type": "^19.6.0", "form-data": "^4.0.1", "got": "^14.4.3", "html2canvas": "^1.4.1", "idb": "^8.0.1", "ip": "^2.0.1", "js-beautify": "^1.15.1", "js-cookie": "^3.0.5", "js-yaml": "^4.1.0", "json-bigint": "^1.0.0", "json-stable-stringify": "^1.2.1", "json5": "^2.2.3", "jsontoxml": "^1.0.1", "jszip": "^3.10.1", "koa": "^2.15.3", "lodash-es": "^4.17.21", "mime": "^4.0.6", "mitt": "^3.0.1", "mockjs": "^1.1.0", "monaco-editor": "^0.52.2", "nanoid": "^5.0.7", "nprogress": "^0.2.0", "pinia": "^2.2.0", "prettier": "^3.4.2", "set-cookie-parser": "^2.7.1", "vue": "^3.5.13", "vue-i18n": "^10.0.8", "vue-router": "^4.4.2", "vuedraggable": "^4.1.0", "vuex": "^4.1.0", "ws": "^8.18.3"}, "devDependencies": {"@types/got": "^9.6.12", "@types/ip": "^1.1.3", "@types/js-beautify": "^1.14.3", "@types/js-cookie": "^3.0.6", "@types/js-yaml": "^4.0.9", "@types/json-bigint": "^1.0.4", "@types/jsontoxml": "^1.0.6", "@types/lodash": "^4.17.7", "@types/mockjs": "^1.0.10", "@types/nprogress": "^0.2.3", "@types/set-cookie-parser": "^2.4.10", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "chokidar": "^4.0.1", "electron": "36.2.0", "electron-builder": "^26.0.12", "esbuild": "^0.25.8", "eslint": "^9.8.0", "openapi-types": "^12.1.3", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.26.5", "typescript": "~5.6.2", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^6.0.3", "vite-plugin-singlefile": "^2.2.0", "vue-tsc": "^2.1.10"}, "main": "dist/main/main.mjs", "build": {"compression": "maximum", "productName": "Apiflow", "appId": "cn.apiflow", "copyright": "Copyright © 2025 TrueLeaf Team", "directories": {"app": ".", "buildResources": "build", "output": "release"}, "files": ["dist/**/*", "package.json"], "extraResources": [{"from": "public/icons", "to": "icons"}], "win": {"target": [{"target": "nsis", "arch": ["x64", "arm64"]}, {"target": "portable", "arch": ["x64", "arm64"]}], "icon": "public/icons/icon.ico", "artifactName": "${productName}-${version}-${arch}.${ext}", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "ApiFlow"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "public/icons/icon.icns", "category": "public.app-category.developer-tools", "artifactName": "${productName}-${version}-${arch}.${ext}", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "dmg": {"title": "${productName} ${version}", "icon": "public/icons/icon.icns", "window": {"width": 540, "height": 380}, "contents": [{"x": 410, "y": 150, "type": "link", "path": "/Applications"}, {"x": 130, "y": 150, "type": "file"}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64", "arm64"]}, {"target": "deb", "arch": ["x64", "arm64"]}], "icon": "public/icons", "category": "Development", "artifactName": "${productName}-${version}-${arch}.${ext}", "desktop": {"entry": {"Name": "ApiFlow", "Comment": "Visual API design tool", "Categories": "Development;IDE;", "Keywords": "api;design;development;rest;graphql;"}}}, "publish": {"provider": "generic", "url": "https://releases.example.com/"}}, "engines": {"node": ">=20.0.0"}}