## 国际化
- 所有涉及多语言地方必须使用i18n进行转换,vue文件中使用useTranslation

## 代码风格
- 所有操作相关事件必须使用handle开头
- 禁止添加空白行
- 只添加关键的代码注释
- 只采用最新的js和ts语法，不需要考虑兼容性
- 函数定义优先使用箭头函数
- 必须添加末尾分号
- 注释必须为中文

## 样式
- 代码中css变量必须使用index.css中的变量
- 禁止使用标签选择器
- 优先使用index.css中预置的类名
- 不修改element-plus的样式
- 所有涉及zIndex地方，禁止硬编码数值，可以使用index.css中符合名称的变量，也可以在index.css单独定义zIndex变量

## 其他
- 禁止添加md说明文件
- 不要添加总结文档
- 项目运行命令为npm run dev:standalone
- 忽略share.html文件的修改
- 禁止给出修改总结
- 生成默认id的地方必须使用helper中的uuid方法
