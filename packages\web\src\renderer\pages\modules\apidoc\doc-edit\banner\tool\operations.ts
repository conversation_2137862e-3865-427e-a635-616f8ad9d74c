import { i18n } from '@/i18n';

export const originOperaions = [
  {
    name: i18n.global.t('新增文件夹'),
    icon: "#iconxinzengwenjian",
    op: "addRootFolder",
    shortcut: [],
    pin: true,
  },
  {
    name: i18n.global.t('新增文件'),
    icon: "#iconwenjian",
    op: "addRootFile",
    shortcut: [],
    pin: true,
  },
  {
    name: i18n.global.t('刷新banner'),
    icon: "#iconshuaxin",
    op: "freshBanner",
    shortcut: [],
    pin: true,
    viewOnly: true,
  },
  {
    name: i18n.global.t('全局设置'),
    icon: "#iconshezhi",
    op: "config",
    shortcut: ["Ctrl", ","],
    pin: true,
  },
  {
    name: i18n.global.t('Cookie管理'),
    icon: "#iconCookies",
    op: "cookies",
    shortcut: ["Ctrl", "Alt", "C"],
    pin: true,
  },
  {
    name: i18n.global.t('回收站'),
    icon: "#iconhuish<PERSON>zhan",
    op: "recycler",
    shortcut: ["Ctrl", "Alt", "R"],
    pin: true,
  },
  {
    name: i18n.global.t('项目分享'),
    icon: "#iconlink",
    op: "generateLink",
    shortcut: ["Ctrl", "L"],
    pin: false,
  },
  {
    name: i18n.global.t('导出文档'),
    icon: "#icondaochu1",
    op: "exportDoc",
    shortcut: ["Ctrl", "E"],
    pin: false,
  },
  {
    name: i18n.global.t('导入文档'),
    icon: "#icondaoru",
    op: "importDoc",
    shortcut: ["Ctrl", "I"],
    pin: false,
  },
  // {
  //   name: "操作审计",
  //   icon: "#iconlishi",
  //   op: "history",
  //   shortcut: ["Ctrl", "H"],
  //   pin: false,
  //   viewOnly: true,
  // },
  {
    name: i18n.global.t('生成代码'),
    icon: "#iconshengchengdaima",
    op: "hook",
    shortcut: ["Ctrl", "H"],
    pin: false,
    viewOnly: true,
  },
  {
    name: i18n.global.t('全局变量'),
    icon: "#iconvariable",
    op: "variable",
    shortcut: [],
    pin: false,
    viewOnly: true,
  },
  {
    name: i18n.global.t('接口编排'),
    icon: "#iconbianpaixin",
    op: "apiflow",
    shortcut: [],
    pin: false,
    viewOnly: true,
  },
];
